<?php

declare(strict_types=1);

namespace Modules\Analytics\Priv\Services;

use App\Company;
use App\Instance;
use App\Repositories\Pagination\PaginatorInterface;
use App\User;
use Illuminate\Support\Collection;
use Modules\Analytics\Priv\Dtos\NewAccountDimensionDto;
use Modules\Analytics\Priv\Entities\AccountDimension;
use Modules\Analytics\Priv\Repositories\AccountDimensionRepository;
use Modules\Analytics\Pub\Enums\AccountDimensionVisibilityEnum;
use Modules\Common\Dtos\PageInterface;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class AccountDimensionService
{
    protected AccountDimensionRepository $accountDimensionRepository;

    public function __construct(AccountDimensionRepository $accountDimensionRepository)
    {
        $this->accountDimensionRepository = $accountDimensionRepository;
    }

    public function create(NewAccountDimensionDto $dto, User $actor): AccountDimension
    {
        $accountDimension = new AccountDimension();
        $accountDimension->code = $dto->getCode();
        $accountDimension->name = $dto->getName();
        $accountDimension->order = $dto->getOrder();
        $accountDimension->instance_id = $dto->getInstance()->id;
        $accountDimension->company_id = $dto->getCompanyId();
        $accountDimension->visibility = $dto->getAccountDimensionVisibilityEnum();
        $accountDimension->is_active = $dto->getIsActive();
        $accountDimension->created_by = $actor->id;

        $accountDimension = $this->accountDimensionRepository->persist($accountDimension);

        return $this->accountDimensionRepository->getFreshModel($accountDimension);
    }

    public function update(AccountDimension $accountDimension, NewAccountDimensionDto $dto, User $actor): AccountDimension
    {
        $accountDimension->code = $dto->getCode();
        $accountDimension->name = $dto->getName();
        $accountDimension->order = $dto->getOrder();
        $accountDimension->company_id = $dto->getCompanyId();
        $accountDimension->visibility = $dto->getAccountDimensionVisibilityEnum();
        $accountDimension->is_active = $dto->getIsActive();
        $accountDimension->updated_by = $actor->id;

        $accountDimension = $this->accountDimensionRepository->persist($accountDimension);

        return $this->accountDimensionRepository->getFreshModel($accountDimension);
    }

    public function getBySlug(string $slug): AccountDimension
    {
        return $this->accountDimensionRepository->findBySlug($slug);
    }

    /**
     * @param int $instanceId
     * @param int|null $companyId
     * @param array<AccountDimensionVisibilityEnum> $visibilities
     * @return Collection
     */
    public function findAllActiveForCompany(int $instanceId, ?int $companyId, ?array $visibilites = null)
    {
        return $this->accountDimensionRepository->findAllActiveForInstanceAndCompany($instanceId, $companyId, $visibilites);
    }

    /**
     * @param Instance $instance
     * @param array<AccountDimensionVisibilityEnum> $visibilities
     * @return Collection
     */
    public function findAllActiveForInstance(Instance $instanceId, ?array $visibilities = null)
    {
        return $this->accountDimensionRepository->findAllByInstance($instanceId, $visibilities);
    }

    public function list(PageInterface $page, Instance $instance): PaginatorInterface
    {
        return $this->accountDimensionRepository->getForCurrentInstance($page, $instance);
    }

    public function getByCodeAndInstanceAndCompany(string $code, Instance $instance, ?Company $company = null): ?AccountDimension
    {
        return $this->accountDimensionRepository->findByCodeForInstanceAndCompany($instance, $company, $code);
    }

    public function getById(int $id): ?AccountDimension
    {
        try {
            return $this->accountDimensionRepository->findById($id);
        } catch (NotFoundHttpException $e) {
            return null;
        }
    }

    public function getForInstance(Instance $instance): Collection
    {
        return $this->accountDimensionRepository->findAllByInstance($instance);
    }
}
