<?php

declare(strict_types=1);

namespace Modules\Users\Priv\Http\Controllers;

use App\Http\Responses\UserBasicResponse;
use App\Http\Responses\AccountDimensionWithItemsResponse;
use App\User;
use Illuminate\Contracts\Auth\Guard;
use Illuminate\Contracts\Support\Responsable;
use Illuminate\Routing\Controller;
use Modules\Analytics\Priv\Services\AccountDimensionService;
use Modules\Users\Priv\Factory\NewUserDtoFactory;
use Modules\Users\Priv\Http\Dto\ChangeSensitiveDataDto;
use Modules\Users\Priv\Http\Requests\NewUserRequest;
use Modules\Users\Priv\Services\UserService;

class UserController extends Controller
{
    /**
     * @var Guard
     */
    protected $auth;

    /**
     * @var UserService
     */
    protected $userService;

    /**
     * @var NewUserDtoFactory
     */
    protected $newUserDtoFactory;

    /**
     * @var AccountDimensionService
     */
    protected $accountDimensionService;

    /**
     * UserController constructor.
     * @param Guard $auth
     * @param UserService $userService
     * @param NewUserDtoFactory $newUserDtoFactory
     * @param AccountDimensionService $accountDimensionService
     */
    public function __construct(
        Guard $auth, 
        UserService $userService, 
        NewUserDtoFactory $newUserDtoFactory,
        AccountDimensionService $accountDimensionService
    ) {
        $this->auth = $auth;
        $this->userService = $userService;
        $this->newUserDtoFactory = $newUserDtoFactory;
        $this->accountDimensionService = $accountDimensionService;
    }

    public function store(NewUserRequest $request): Responsable
    {
        /** @var User $currentUser */
        $currentUser = $this->auth->user();

        $newUserDto = $this->newUserDtoFactory->create($request);

        $user = $this->userService->create($newUserDto, $currentUser);

        return UserBasicResponse::item($user)->addInfo(trans('user.profile-has-been-saved'));
    }

    public function sensitiveDataUpdate(ChangeSensitiveDataDto $changeSensitiveDataDto): Responsable
    {
        $user = $this->userService->changeSensitiveData($changeSensitiveDataDto);

        return UserBasicResponse::item($user)->addInfo(trans('user.profile-has-been-saved'));
    }

    public function accountDimensions(): Responsable
    {
        /** @var User $currentUser */
        $currentUser = $this->auth->user();
        
        $accountDimensions = $this->accountDimensionService->getForInstance($currentUser->instance);

        return AccountDimensionWithItemsResponse::collection($accountDimensions);
    }
}
