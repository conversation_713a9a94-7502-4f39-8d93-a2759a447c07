<?php

declare(strict_types=1);

namespace Modules\Users\Priv\Factory;

use App\Instance;
use App\Repositories\CompanyRepository;
use App\Repositories\GroupRepository;
use App\Repositories\InstanceRepository;
use App\Repositories\UserRepository;
use App\Services\Language\LanguageService;
use App\User;
use Illuminate\Contracts\Auth\Guard;
use Modules\Analytics\Pub\Facades\AccountDimensionItemFacade;
use Modules\Users\Priv\Rules\AccountDimensionRule;
use Modules\Users\Priv\Rules\CompanyChangeRule;
use Modules\Users\Priv\Rules\MpkRule;

use Modules\Users\Priv\Rules\SupervisorRule;
use Modules\Users\Priv\Rules\UserEmailRule;
use Modules\Users\Priv\Rules\UserErpIdRule;
use Modules\Users\Priv\Rules\UserHrIdRule;
use Modules\Analytics\Priv\Services\AccountDimensionService;
use Modules\Users\Priv\Services\GradeService;
use Modules\Users\Pub\Interfaces\UpdateUserRequestInterface;

class UpdateUserDtoFactory extends NewUserDtoFactory
{
    public function __construct(
        Guard $auth,
        GradeService $gradeService,
        GroupRepository $groupRepository,
        UserRepository $userRepository,
        LanguageService $languageService,
        InstanceRepository $instanceRepository,
        AccountDimensionRule $accountDimensionRule,
        AccountDimensionService $accountDimensionService,
        AccountDimensionItemFacade $accountDimensionItemFacade,
        CompanyRepository $companyRepository
    ) {
        parent::__construct(
            $auth,
            $gradeService,
            $groupRepository,
            $userRepository,
            $languageService,
            $instanceRepository,
            $accountDimensionRule,
            $accountDimensionService,
            $accountDimensionItemFacade,
            $companyRepository,
        );
    }

    /**
     * @param UpdateUserRequestInterface $request
     * @return array
     */
    protected function rules($request): array
    {
        $rules = parent::rules($request);

        /** @var Instance $instance */
        $instance = $this->instanceRepository->findById($request->getInstanceId());
        $instanceId = $instance->id;

        /** @var User $user */
        $user = $this->userRepository->findBySlug($request->getSlug());

        $company = $request->getCompanyId() ? $this->companyRepository->findById($request->getCompanyId()) : $user->company;

        $rules[$request->getCompanyIdKey()][] = new CompanyChangeRule($this->userRepository, $user);
        $rules[$request->getMpkIdKey()] = ['required', 'numeric', new MpkRule($instanceId, $request->getCompanyId())];
        $rules[$request->getEmailKey()] = [
            'required',
            'email',
            'max:255',
            new UserEmailRule($request->getEmployeeUniqueIdentifier(), $instanceId, $user),
        ];
        $rules[$request->getSupervisorIdKey()] = [
            'nullable',
            'numeric',
            'exists:users,id,instance_id,' . $instanceId,
            new SupervisorRule($user),
        ];
        $rules[$request->getAssistantIdKey()] = ['nullable', 'numeric', 'exists:users,id,instance_id,' . $instanceId];
        $rules[$request->getHrIdKey()] = [
            'nullable',
            'string',
            new UserHrIdRule($request->getEmployeeUniqueIdentifier(), $instanceId, $company->id, $user),
        ];
        $rules[$request->getErpIdKey()] = [
            'nullable',
            'string',
            new UserErpIdRule($request->getEmployeeUniqueIdentifier(), $instanceId, $company->id, $user),
        ];
        $rules[$request->getEmployeeUniqueIdentifierKey()] = [
            'required',
            'string',
        ];

        return $rules;
    }
}
